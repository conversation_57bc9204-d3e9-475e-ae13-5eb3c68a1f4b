FROM continuumio/miniconda3:latest AS conda-env

# Create conda environments
RUN conda create -n codeact-test python=3.11 -y && \
    conda run -n codeact-test pip install \
    numpy pandas matplotlib seaborn scikit-learn \
    requests beautifulsoup4 plotly

RUN conda create -n codeact-data python=3.11 -y && \
    conda run -n codeact-data pip install \
    numpy pandas matplotlib seaborn scikit-learn \
    jupyter sqlalchemy psycopg2-binary pymongo redis

# Main application stage
FROM python:3.13-slim

WORKDIR /app

# System dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
        curl ca-certificates git gnupg unzip \
        r-base r-base-dev build-essential && \
    rm -rf /var/lib/apt/lists/*

# Copy conda from first stage
COPY --from=conda-env /opt/conda /opt/conda
ENV PATH="/opt/conda/bin:${PATH}"

# uv installation
RUN curl -LsSf https://astral.sh/uv/install.sh | sh
ENV PATH="/root/.local/bin:${PATH}"
RUN uv --version && uvx --version

# Node.js
RUN curl -fsSL https://deb.nodesource.com/setup_20.x | bash - && \
    apt-get install -y nodejs && \
    corepack enable && \
    node --version && npx --version

# Deno
RUN curl -fsSL https://deno.land/install.sh | sh -s -- -y
ENV PATH="/root/.deno/bin:${PATH}"
RUN deno --version

# Python dependencies
COPY requirements.txt ./
RUN pip install --no-cache-dir -r requirements.txt

# Application code
COPY . .

# Set default execution environment
ENV CODEACT_CONDA_ENV=codeact-test

EXPOSE 8000
CMD ["python", "codeact_a2a_server.py"]
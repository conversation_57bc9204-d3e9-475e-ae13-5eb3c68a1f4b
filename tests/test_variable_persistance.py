"""
Enhanced test client for the CodeAct A2A server with improved multi-turn testing.
This script focuses on testing the variable persistence between turns.
"""

import asyncio
import traceback
import uuid
import json
from typing import Any

import pytest
import httpx
from a2a.client import A2AClient
from a2a.types import (
    GetTaskRequest,
    GetTaskResponse,
    MessageSendParams,
    Role,
    SendMessageRequest,
    SendMessageResponse,
    SendMessageSuccessResponse,
    Task,
    TaskQueryParams,
)

# Server configuration - make sure to include the trailing slash
AGENT_URL = "http://localhost:8000/rpc/"
VERBOSE = True  # Set to True for more detailed diagnostic information

def debug_print(message: str, obj: Any = None) -> None:
    """Print debug information if VERBOSE is True."""
    if not VERBOSE:
        return
    
    print(f"\n[DEBUG] {message}")
    if obj is not None:
        if hasattr(obj, "model_dump"):
            print(json.dumps(obj.model_dump(mode="json", exclude_none=True), indent=2))
        elif hasattr(obj, "json"):
            print(obj.json(indent=2))
        elif hasattr(obj, "dict"):
            print(json.dumps(obj.dict(), indent=2))
        else:
            print(obj)
    print()


def create_message_payload(
    text: str, context_id: str | None = None, task_id: str | None = None
) -> dict[str, Any]:
    """Helper to create a message payload for the CodeAct agent."""
    payload: dict[str, Any] = {
        "message": {
            "role": Role.user.value,
            "parts": [{"kind": "text", "text": text}],
            "messageId": str(uuid.uuid4()),
        },
    }

    if context_id:
        payload["contextId"] = context_id
        payload["message"]["contextId"] = context_id
    
    if task_id:
        payload["taskId"] = task_id
        payload["message"]["taskId"] = task_id
    
    debug_print("Created message payload", payload)
    return payload


def print_response(response: Any, description: str) -> None:
    """Print a formatted response from the agent."""
    print(f"\n{'=' * 20} {description} {'=' * 20}")
    if hasattr(response, "root"):
        print(f"{response.root.model_dump_json(exclude_none=True)}\n")
    else:
        print(f"{response.model_dump_json(exclude_none=True)}\n")
    print('=' * (40 + len(description) + 2))


@pytest.mark.asyncio
async def test_variable_persistence(client: A2AClient) -> None:
    """Test multi-turn interaction with variable persistence."""
    print("\n[TEST] Multi-turn variable persistence")
    
    # Create a shared context ID for the entire conversation
    context_id = str(uuid.uuid4())
    print(f"Using shared context_id for all turns: {context_id}")
    
    try:
        # First turn - define a variable
        first_turn_text = "# Define a dictionary with user information\nuser_info = {'name': 'John', 'age': 30, 'location': 'New York'}\nprint(f'Created user info: {user_info}')"
        
        first_turn_payload = create_message_payload(
            text=first_turn_text,
            context_id=context_id
        )
        
        first_request = SendMessageRequest(params=MessageSendParams(**first_turn_payload))
        debug_print("Sending first turn request with payload", first_turn_payload)
        
        # Send first message
        first_response: SendMessageResponse = await client.send_message(first_request)
        print_response(first_response, "First Turn - Define User Info Dictionary")
        debug_print("Received first_response", first_response)

        # Extract task ID from first turn
        if not isinstance(first_response.root, SendMessageSuccessResponse) or not isinstance(
            first_response.root.result, Task
        ):
            print("Error: First turn response did not contain a valid task.")
            return
            
        task = first_response.root.result
        first_task_id = task.id
        print(f"First task ID: {first_task_id}")
        
        # Wait briefly to ensure processing completes
        await asyncio.sleep(1)
        
        # Second turn - access the previously defined variable
        second_turn_text = "# Access the user's name from user_info\nuser_name = user_info['name']\nprint(f'User name is: {user_name}')"
        
        second_turn_payload = create_message_payload(
            text=second_turn_text,
            context_id=context_id  # Use same context_id for variable persistence
        )
        
        second_request = SendMessageRequest(params=MessageSendParams(**second_turn_payload))
        debug_print("Sending second turn request", second_turn_payload)
        
        # Send second message
        second_response = await client.send_message(second_request)
        print_response(second_response, "Second Turn - Access User Name")
        debug_print("Received second_response", second_response)
        
        # Extract task ID from second turn
        if not isinstance(second_response.root, SendMessageSuccessResponse) or not isinstance(
            second_response.root.result, Task
        ):
            print("Error: Second turn response did not contain a valid task.")
            return
            
        second_task = second_response.root.result
        second_task_id = second_task.id
        print(f"Second task ID: {second_task_id}")
        
        # Wait briefly to ensure processing completes
        await asyncio.sleep(1)
        
        # Third turn - modify the dictionary
        third_turn_text = "# Update user's age\nuser_info['age'] = 31\nprint(f'Updated user info: {user_info}')"
        
        third_turn_payload = create_message_payload(
            text=third_turn_text,
            context_id=context_id  # Use same context_id for variable persistence
        )
        
        third_request = SendMessageRequest(params=MessageSendParams(**third_turn_payload))
        debug_print("Sending third turn request", third_turn_payload)
        
        # Send third message
        third_response = await client.send_message(third_request)
        print_response(third_response, "Third Turn - Update User Age")
        debug_print("Received third_response", third_response)
        
        # Wait briefly to ensure processing completes
        await asyncio.sleep(1)
        
        # Fourth turn - verify all changes are persistent
        fourth_turn_text = "# Print all variables to verify persistence\nprint(f'User info: {user_info}')\nprint(f'User name: {user_name}')"
        
        fourth_turn_payload = create_message_payload(
            text=fourth_turn_text,
            context_id=context_id  # Use same context_id for variable persistence
        )
        
        fourth_request = SendMessageRequest(params=MessageSendParams(**fourth_turn_payload))
        debug_print("Sending fourth turn request", fourth_turn_payload)
        
        # Send fourth message
        fourth_response = await client.send_message(fourth_request)
        print_response(fourth_response, "Fourth Turn - Verify All Variables")
        debug_print("Received fourth_response", fourth_response)
        
    except Exception as e:
        print(f"Error in variable persistence test: {e}")
        traceback.print_exc()


async def main() -> None:
    """Main function to run the variable persistence test."""
    print(f"Testing CodeAct agent at {AGENT_URL}")
    
    try:
        # First check if the server is available 
        async with httpx.AsyncClient(follow_redirects=True) as check_client:
            try:
                health_response = await check_client.get(AGENT_URL.rstrip("/") + "/../health")
                if health_response.status_code == 200:
                    print(f"Server health check passed: {health_response.text}")
                else:
                    print(f"Server health check returned status {health_response.status_code}")
            except Exception as e:
                print(f"Health check failed, but we'll still try to connect: {e}")
        
        async with httpx.AsyncClient(follow_redirects=True) as httpx_client:
            # Connect to the agent using the A2A client
            try:
                print("Connecting to agent...")
                client = await A2AClient.get_client_from_agent_card_url(
                    httpx_client, AGENT_URL
                )
                print("Successfully connected to CodeAct agent.")
                
                # Run the variable persistence test
                await test_variable_persistence(client)
            except Exception as e:
                print(f"Error connecting to agent: {e}")
                traceback.print_exc()

    except Exception as e:
        traceback.print_exc()
        print(f"Error: {e}")
        print("Make sure the CodeAct A2A server is running.")


if __name__ == "__main__":
    asyncio.run(main())
"""
Test MCP filesystem tools in addition to Python file operations
"""

import asyncio
import uuid
import pytest
import httpx
from a2a.client import A2<PERSON>lient
from a2a.types import MessageSendParams, SendMessageRequest

AGENT_URL = "http://localhost:8000/rpc/"

@pytest.mark.asyncio
async def test_mcp_filesystem():
    async with httpx.AsyncClient(follow_redirects=True) as httpx_client:
        client = await A2AClient.get_client_from_agent_card_url(httpx_client, AGENT_URL)
        context_id = str(uuid.uuid4())
        
        print("🧪 Testing Python file operations")
        
        # Test MCP file creation
        request = SendMessageRequest(params=MessageSendParams(
            message={
                "role": "user",
                "parts": [{"kind": "text", "text": """
# Test basic file operations
import os

# Create file
with open('/tmp/basic_test.txt', 'w') as f:
    f.write('Hello from Python file operations!')
    
print("✅ File created")

# Read file
with open('/tmp/basic_test.txt', 'r') as f:
    content = f.read()
    
print(f"File content: {content}")

# List directory
files = os.listdir('/tmp')
print(f"Files in /tmp: {files}")

# File info
stat = os.stat('/tmp/basic_test.txt')
print(f"File size: {stat.st_size} bytes")
"""}],
                "messageId": str(uuid.uuid4()),
            },
            contextId=context_id
        ))
        
        response = await client.send_message(request)
        
        if hasattr(response.root, 'result') and response.root.result.artifacts:
            part = response.root.result.artifacts[-1].parts[0]
            output = part.root.text if hasattr(part, 'root') else str(part)
            print(f"Output: {output}")
        
        print("✅ Python file operations test completed")

if __name__ == "__main__":
    asyncio.run(test_mcp_filesystem())

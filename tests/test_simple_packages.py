#!/usr/bin/env python3
"""Simple test for package installation functionality."""

from package_installer import PackageInstaller

def test_package_detection():
    """Test package installation command detection."""
    installer = PackageInstaller(conda_env="codeact-test")
    
    code = """
!pip install requests
import numpy as np
!conda install scipy -y
print("Hello")
install.packages("jsonlite")
"""
    
    commands = installer.detect_install_commands(code)
    print(f"Detected commands: {commands}")
    
    expected = [
        ('python', 'pip', 'requests'),
        ('python', 'conda', 'scipy -y'),
        ('r', 'r', 'jsonlite')
    ]
    
    if commands == expected:
        print("✅ Package detection test passed")
    else:
        print("❌ Package detection test failed")
        print(f"Expected: {expected}")
        print(f"Got: {commands}")

if __name__ == "__main__":
    test_package_detection()

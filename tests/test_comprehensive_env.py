#!/usr/bin/env python3
"""
Comprehensive test suite for CodeAct agent with conda environment support.
Tests environment execution with real data science packages.
"""

import os
import sys
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import pytest
from codeact_graph import eval_with_env

class TestEnvironmentExecution:
    """Test conda environment execution capabilities."""
    
    def setup_method(self):
        """Set up test environment."""
        os.environ["CODEACT_CONDA_ENV"] = "codeact-test"
        print(f"\n[TEST-SETUP] Set CODEACT_CONDA_ENV=codeact-test")
    
    def test_basic_environment(self):
        """Test basic environment detection."""
        print("\n[TEST] Testing basic environment detection")
        result, _ = eval_with_env("import sys; print(sys.executable)", {})
        print(f"[TEST] Result: {result}")
        assert "codeact-test" in result
        print("[TEST] ✅ Basic environment test passed")
    
    def test_numpy_operations(self):
        """Test NumPy operations in environment."""
        print("\n[TEST] Testing NumPy operations")
        code = """
import numpy as np
arr = np.array([1, 2, 3, 4, 5])
mean_val = np.mean(arr)
print(f"Array: {arr}")
print(f"Mean: {mean_val}")
"""
        print(f"[TEST] Executing code: {code[:50]}...")
        result, new_vars = eval_with_env(code, {})
        print(f"[TEST] Result: {result}")
        print(f"[TEST] New vars: {list(new_vars.keys())}")
        assert "Array: [1 2 3 4 5]" in result
        assert "Mean: 3.0" in result
        # Variables don't persist across env boundary - this is expected
        print("[TEST] ✅ NumPy test passed")
    
    def test_pandas_operations(self):
        """Test Pandas operations in environment."""
        print("\n[TEST] Testing Pandas operations")
        code = """
import pandas as pd
data = {'name': ['Alice', 'Bob'], 'age': [25, 30]}
df = pd.DataFrame(data)
print(f"DataFrame shape: {df.shape}")
print(df.to_string())
"""
        print(f"[TEST] Executing code: {code[:50]}...")
        result, new_vars = eval_with_env(code, {})
        print(f"[TEST] Result: {result}")
        print(f"[TEST] New vars: {list(new_vars.keys())}")
        assert "DataFrame shape: (2, 2)" in result
        assert "Alice" in result
        print("[TEST] ✅ Pandas test passed")
    
    def test_matplotlib_plotting(self):
        """Test matplotlib in headless environment."""
        code = """
import matplotlib
matplotlib.use('Agg')  # Headless backend
import matplotlib.pyplot as plt
import numpy as np

x = np.linspace(0, 10, 100)
y = np.sin(x)
plt.plot(x, y)
plt.title("Sine Wave")
plt.savefig('/tmp/test_plot.png')
plt.close()
print("✅ Plot saved successfully")
"""
        result, _ = eval_with_env(code, {})
        assert "✅ Plot saved successfully" in result
    
    def test_requests_library(self):
        """Test requests library for HTTP calls."""
        code = """
import requests
try:
    response = requests.get('https://httpbin.org/get', timeout=5)
    print(f"Status: {response.status_code}")
    print("✅ HTTP request successful")
except Exception as e:
    print(f"Request failed: {e}")
"""
        result, _ = eval_with_env(code, {})
        assert ("Status: 200" in result and "✅ HTTP request successful" in result) or "Request failed" in result
    
    def test_variable_persistence_across_executions(self):
        """Test that variables don't persist across environment executions (expected behavior)."""
        # First execution
        result1, vars1 = eval_with_env("import numpy as np; data = np.array([1, 2, 3]); print('First execution')", {})
        
        # Variables don't persist across environment boundary - this is expected
        # Each environment execution is isolated
        assert "First execution" in result1
        print("[TEST] ✅ Environment isolation test passed")
    
    def test_complex_data_analysis(self):
        """Test complex data analysis workflow."""
        code = """
import pandas as pd
import numpy as np

# Create sample dataset
np.random.seed(42)
data = {
    'temperature': np.random.normal(20, 5, 100),
    'humidity': np.random.normal(60, 10, 100),
    'pressure': np.random.normal(1013, 15, 100)
}
df = pd.DataFrame(data)

# Basic statistics
temp_mean = df['temperature'].mean()
humidity_std = df['humidity'].std()
correlation = df['temperature'].corr(df['humidity'])

print(f"Temperature mean: {temp_mean:.2f}")
print(f"Humidity std: {humidity_std:.2f}")
print(f"Temp-Humidity correlation: {correlation:.3f}")
print(f"Dataset shape: {df.shape}")
"""
        result, new_vars = eval_with_env(code, {})
        assert "Temperature mean:" in result
        assert "Humidity std:" in result
        assert "correlation:" in result
        assert "Dataset shape: (100, 3)" in result
    
    def test_error_handling(self):
        """Test error handling in environment execution."""
        code = "import non_existent_module"
        result, _ = eval_with_env(code, {})
        assert "Error during execution" in result
        assert "ModuleNotFoundError" in result
    
    def test_fallback_execution(self):
        """Test fallback to direct execution when no environment set."""
        # Clear environment variables
        os.environ.pop("CODEACT_CONDA_ENV", None)
        os.environ.pop("CODEACT_VENV_PATH", None)
        
        result, _ = eval_with_env("print('direct execution test')", {})
        assert "direct execution test" in result
        
        # Restore environment
        os.environ["CODEACT_CONDA_ENV"] = "codeact-test"

if __name__ == "__main__":
    pytest.main([__file__, "-v"])

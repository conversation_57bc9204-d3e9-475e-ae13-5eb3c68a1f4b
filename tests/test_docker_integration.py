#!/usr/bin/env python3
"""Test CodeAct agent running in Docker container"""

import subprocess
import time
import requests
import sys

def test_docker_integration():
    """Test the full Docker setup"""
    print("🧪 Testing Docker Integration")
    print("=" * 40)
    
    # Build with docker compose
    print("[DOCKER] Building with docker compose...")
    result = subprocess.run(["docker", "compose", "build"], capture_output=True, text=True)
    if result.returncode != 0:
        print(f"❌ Build failed: {result.stderr}")
        return False
    print("✅ Build successful")
    
    # Start container
    print("[DOCKER] Starting container...")
    subprocess.run(["docker", "compose", "up", "-d"], capture_output=True)
    
    # Wait for startup (longer wait for conda environment)
    print("[DOCKER] Waiting for server startup...")
    time.sleep(45)  # Increased wait time
    
    # Test health endpoint with retries
    for attempt in range(3):
        try:
            response = requests.get("http://localhost:8000/health", timeout=10)
            if response.status_code == 200:
                print("✅ Health check passed")
                break
            else:
                print(f"⚠️ Health check attempt {attempt+1}: {response.status_code}")
        except Exception as e:
            print(f"⚠️ Health check attempt {attempt+1} failed: {e}")
            if attempt < 2:
                time.sleep(10)
            else:
                print("❌ Health check failed after 3 attempts")
                return False
    
    # Test A2A endpoint
    try:
        response = requests.get("http://localhost:8000/rpc/", timeout=10)
        print(f"✅ A2A endpoint accessible: {response.status_code}")
    except Exception as e:
        print(f"❌ A2A endpoint failed: {e}")
        return False
    
    # Cleanup
    print("[DOCKER] Stopping container...")
    subprocess.run(["docker", "compose", "down"], capture_output=True)
    
    print("✅ Docker integration test passed")
    return True

if __name__ == "__main__":
    success = test_docker_integration()
    sys.exit(0 if success else 1)

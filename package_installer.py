"""Package installer utility for CodeAct agent."""

import subprocess
import re
import os
import logging
from typing import List, Optional, Tuple, Dict, Any

logger = logging.getLogger(__name__)

class PackageInstaller:
    """Handles dynamic package installation for Python and R."""
    
    def __init__(self, conda_env: Optional[str] = None, venv_path: Optional[str] = None):
        self.conda_env = conda_env
        self.venv_path = venv_path
        
    def detect_install_commands(self, code: str) -> List[Tuple[str, str, str]]:
        """Detect package installation commands in code.
        
        Returns:
            List of (command_type, package_manager, packages) tuples
        """
        install_commands = []
        lines = code.split('\n')
        
        for line in lines:
            line = line.strip()
            
            # Python package installations
            if line.startswith('!pip install'):
                packages = line.replace('!pip install', '').strip()
                install_commands.append(('python', 'pip', packages))
            elif line.startswith('!conda install'):
                packages = line.replace('!conda install', '').strip()
                install_commands.append(('python', 'conda', packages))
            elif line.startswith('!uv add'):
                packages = line.replace('!uv add', '').strip()
                install_commands.append(('python', 'uv', packages))
            
            # R package installations
            elif 'install.packages(' in line:
                # Extract package names from install.packages() calls
                match = re.search(r'install\.packages\(["\']([^"\']+)["\']', line)
                if match:
                    package = match.group(1)
                    install_commands.append(('r', 'r', package))
                    
        return install_commands
    
    def install_python_package(self, manager: str, packages: str) -> Tuple[bool, str]:
        """Install Python packages using specified manager."""
        try:
            if manager == 'pip':
                if self.conda_env:
                    cmd = ['conda', 'run', '-n', self.conda_env, 'pip', 'install'] + packages.split()
                elif self.venv_path:
                    pip_path = os.path.join(self.venv_path, 'bin', 'pip')
                    cmd = [pip_path, 'install'] + packages.split()
                else:
                    cmd = ['pip', 'install'] + packages.split()
                    
            elif manager == 'conda':
                if self.conda_env:
                    cmd = ['conda', 'install', '-n', self.conda_env, '-y'] + packages.split()
                else:
                    cmd = ['conda', 'install', '-y'] + packages.split()
                    
            elif manager == 'uv':
                # UV doesn't work well with conda envs, use pip fallback
                return self.install_python_package('pip', packages)
            
            else:
                return False, f"Unknown package manager: {manager}"
                
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                return True, f"Successfully installed: {packages}"
            else:
                return False, f"Installation failed: {result.stderr}"
                
        except subprocess.TimeoutExpired:
            return False, f"Installation timeout for: {packages}"
        except Exception as e:
            return False, f"Installation error: {str(e)}"
    
    def install_r_package(self, package: str) -> Tuple[bool, str]:
        """Install R packages."""
        try:
            # Use R command to install package
            cmd = ['R', '--slave', '-e', f'install.packages("{package}", repos="https://cran.rstudio.com/")']
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                return True, f"Successfully installed R package: {package}"
            else:
                return False, f"R package installation failed: {result.stderr}"
                
        except subprocess.TimeoutExpired:
            return False, f"R package installation timeout: {package}"
        except Exception as e:
            return False, f"R package installation error: {str(e)}"
    
    def process_installations(self, code: str) -> Tuple[str, List[str]]:
        """Process all package installations in code and return modified code."""
        install_commands = self.detect_install_commands(code)
        install_results = []
        
        # Remove install commands from code
        modified_code = code
        for cmd_type, manager, packages in install_commands:
            if cmd_type == 'python':
                # Remove pip/conda/uv install lines
                pattern = f'!{manager} install {re.escape(packages)}'
                modified_code = re.sub(pattern, '', modified_code)
                
                success, message = self.install_python_package(manager, packages)
                install_results.append(f"[PACKAGE] {message}")
                
            elif cmd_type == 'r':
                # Don't remove R install.packages() lines as they're part of R code
                success, message = self.install_r_package(packages)
                install_results.append(f"[PACKAGE] {message}")
        
        return modified_code, install_results
    
    def get_installed_packages(self) -> Dict[str, List[str]]:
        """Get list of installed packages."""
        packages = {'python': [], 'r': []}
        
        try:
            # Python packages
            if self.conda_env:
                cmd = ['conda', 'list', '-n', self.conda_env]
            else:
                cmd = ['pip', 'list']
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode == 0:
                # Parse package list (implementation depends on format)
                packages['python'] = ['pip', 'numpy', 'pandas']  # Placeholder
                
            # R packages  
            cmd = ['R', '--slave', '-e', 'cat(installed.packages()[,1], sep="\\n")']
            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode == 0:
                packages['r'] = result.stdout.strip().split('\n')
                
        except Exception as e:
            logger.error(f"Error getting package list: {e}")
            
        return packages

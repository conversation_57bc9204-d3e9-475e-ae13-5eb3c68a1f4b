.PHONY: test test-all test-env test-a2a test-packages test-docker clean build up down logs

# Default target
test: test-all

# Run all tests
test-all:
	@echo "🧪 Running all CodeAct tests..."
	python run_tests.py all

# Environment tests
test-env:
	@echo "🧪 Running environment tests..."
	python run_tests.py env

# A2A protocol tests (requires running server)
test-a2a:
	@echo "🧪 Running A2A tests..."
	python run_tests.py a2a

# Package installation tests
test-packages:
	@echo "🧪 Running package tests..."
	python run_tests.py packages

# Docker integration tests
test-docker:
	@echo "🧪 Running Docker tests..."
	python run_tests.py docker

# Docker operations
build:
	docker compose build

up:
	docker compose up -d

down:
	docker compose down

logs:
	docker compose logs -f

clean:
	docker compose down --volumes --remove-orphans
	docker system prune -f

# Full restart and test
clean-test: clean build up
	@echo "Waiting for server to start..."
	@sleep 50
	@make test-all

# Run tests inside Docker
test-in-docker:
	./test_in_docker.sh all
